import { useEffect, useRef, useState } from "react";
import { Link, useLoaderData } from "react-router-dom";

import { loadStripe, Stripe } from "@stripe/stripe-js";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { Helmet } from 'react-helmet';
import checkCircle from '../../assets/images/check-mark-circle.webp';
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import {
	cardPaymentFailedMutation,
	getAllPlanDataQuery,
	getSubscriptionHistoryQuery,
	planChangeMutation,
	purchasePlanMutation,
	stripePortalLinkQuery
} from "../../utils/api";
import './SubscriptionPlanManager.min.css';

interface PlansPageData {
	has_payment_method: boolean
	has_customer_id: boolean
	current_active_price_id_on_db: string
	current_active_price_id: string
	current_active_plan_id: string
	current_active_plan_name: string
	current_active_period_end_date: string
	stripe_customer_portal: string
	latest_invoice_is_open: boolean
	usage_stats: Array<UsageStat>
	ltd_enabled: boolean
	ltd_plans: Array<string>
}

interface UsageStat {
	title: string
	value: number
	max: number
	combined_string: string
	unlimited: boolean
}

interface PlanFeature {
	label: string
	value: string | number
}

interface PlanTool {
	label: string
	value: string | number
	comingSoon: boolean
}

interface PlanData {
	id: string
	name: string
	metadata: PlanMetadata
	price_id: string
	price_amount: number
	currency_code: string
	articles: number
	credits: number
	sites: number
	annual_plan: boolean
	features: Array<PlanFeature>
	tools: Array<PlanTool>
}

interface PlanMetadata {
	max_articles: number
	max_titles: number
	max_keywords: number
	position: number
	websites: number
	description: string
	popular: boolean
}

interface SubscriptionHistoryData {
	active: boolean
	subscription_id: string
	price_id: string
	plan_name: string
	currency: string
	amount: number
	created: string
	current_period_start: string
	current_period_end: string
}

export default function SubscriptionPlanManager() {
	// ------------------ NON STATE CONSTANTS ------------------
	const pageSizes = [5, 10, 15, 30, 50, 100, 500];

	// ------------------ PAGE DATA ------------------
	const pageData = useLoaderData() as PlansPageData;

	// ------------------ STATES ------------------
	const [stripe, setStripe] = useState<Stripe | null>(null);
	const [currentPriceID, setCurrentPriceID] = useState(pageData.current_active_price_id);
	const [planCardsData, setPlanCardsData] = useState<Array<PlanData>>([]);
	const [tableData, setTableData] = useState<Array<SubscriptionHistoryData>>([]);
	const [purchaseConfirmModalActive, setPurchaseConfirmModalActive] = useState(false);
	const [selectedPlanName, setSelectedPlanName] = useState("");
	const [selectedPriceID, setSelectedPriceID] = useState("");
	const [selectedPriceAmount, setSelectedPriceAmount] = useState(0);
	const [currentPriceAmount, setCurrentPriceAmount] = useState(0);
	const [cancelModalActive, setCancelModalActive] = useState(false);
	const [processingModalActive, setProcessingModalActive] = useState(false);
	const [activeTab, setActiveTab] = useState("plans");
	const [activePricingTab, setActivePricingTab] = useState("monthly");


	// used for disabling modal buttons and other purposes while plan purchase is underway
	const [planPurchaseUnderway, setPlanPurchaseUnderway] = useState(false);
	const [
		stripePortalLink,
		setStripePortalLink
	] = useState<string | null | undefined>("");
	// const [hasPaymentMethod, setHasPaymentMethod] = useState(pageData.has_payment_method);

	// ------------------ QUERIES ------------------
	const allPlanData = useQuery(getAllPlanDataQuery());
	const subscriptionHistory = useQuery(getSubscriptionHistoryQuery());
	const getStripePortalLink = useQuery(stripePortalLinkQuery());

	// ------------------ MUTATIONS ------------------
	const purchasePlan = useMutation(purchasePlanMutation);
	const changePlan = useMutation(planChangeMutation);
	const cardPaymentFailed = useMutation(cardPaymentFailedMutation);

	// ------------------ EFFECTS ------------------

	useEffect(() => {
		loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY as string).then(value => {
			setStripe(value);
		});
	}, [])

	useEffect(() => {
		if (getStripePortalLink.data) {
			setStripePortalLink((getStripePortalLink.data as any)['data']['url']);
		}
	}, [getStripePortalLink.data]);


	useEffect(() => {
		if (allPlanData.data) {
			setPlanCardsData((allPlanData.data as any)['data'] as Array<PlanData>);

			const plans = (allPlanData.data as any)['data'];
			// Find the active plan using the latest 'currentPriceID'
			const activePlan = plans.find(plan => plan.price_id === currentPriceID);

			if (activePlan) {
				setCurrentPriceAmount(activePlan.price_amount);
			}
		}

		document.querySelector(".abun-table-content")?.classList.add("no-bulk-action");
		document.querySelector(".abun-table-filter-buttons")?.remove();
	}, [allPlanData.data, currentPriceID]);

	useEffect(() => {
		if (subscriptionHistory.data) {
			setTableData((subscriptionHistory.data as any)['data']);
		}
	}, [subscriptionHistory.data]);

	// ---------------------------- REFS ----------------------------
	const errorAlertRef = useRef<any>(null);
	const successAlertRef = useRef<any>(null);

	// ------------------ TABLE COLUMN DEFS ------------------
	const columnHelper = createColumnHelper<SubscriptionHistoryData>();
	const columnDefs: ColumnDef<any, any>[] = [
		columnHelper.accessor((row: SubscriptionHistoryData) => row.plan_name, {
			id: 'planName',
			header: () => <span className="header-text">Plan</span>,
			cell: info => info.getValue(),
			enableGlobalFilter: true,
		}),
		columnHelper.display({
			id: 'planAmount',
			header: () => <span className="header-text">Amount</span>,
			cell: props => {
				if (props.row.original.currency === 'inr') {
					return `INR ${props.row.original.amount / 100}`
				} else {
					return `$${props.row.original.amount / 100}`
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		}),
		// columnHelper.accessor((row: SubscriptionHistoryData) => row.created, {
		// 	id: 'subscriptionCreatedDate',
		// 	header: "Purchased On",
		// 	cell: info => info.getValue(),
		// 	enableGlobalFilter: false,
		// }),
		columnHelper.display({
			id: 'subscriptionRenewalDate',
			header: () => <span className="header-text">Renews On</span>,
			cell: props => {
				if (props.row.original.active) {
					return props.row.original.current_period_end
				} else {
					return "---"
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		}),
		columnHelper.display({
			id: 'subscriptionStatus',
			header: () => (<div className={"has-text-centered header-text"}>Status</div>),
			cell: props => {
				if (props.row.original.active) {
					return <span className={"has-text-success has-text-weight-bold"}>Active</span>
				} else {
					return <span className={"has-text-danger has-text-weight-bold"}>Cancelled</span>
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		}),
		columnHelper.display({
			id: 'planCancel',
			header: () => <span className="header-text">Action</span>,
			cell: props => {
				const { active, plan_name } = props.row.original;
				const isAppSumoTier = /^Appsumo Tier \d+$/.test(plan_name);
				if (active && plan_name !== "Trial" && !isAppSumoTier) {
					return <button
						className={"cancel-button"}
						onClick={() => {
							const trialPlanData = tableData.filter(row => row.plan_name === 'Trial')[0];
							setSelectedPlanName(trialPlanData.plan_name);
							setSelectedPriceID(trialPlanData.price_id);
							setSelectedPriceAmount(trialPlanData.amount);
							setCancelModalActive(true);
						}}
					>
						Cancel
					</button>;
				} else {
					return "---";
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		})
	]

	// =======================================================
	// ---------------------- MAIN CODE ----------------------
	// =======================================================

	const hasAppSumo = tableData.some(row => row.plan_name.includes('Appsumo'));

	const filteredTableData = hasAppSumo
		? tableData.filter(row => row.plan_name !== 'Trial')
		: tableData;

	function resetPaymentActions() {
		setPlanPurchaseUnderway(false);
		setSelectedPlanName("");
		setSelectedPriceID("");
		setPurchaseConfirmModalActive(false);
	}

	function paymentSuccessActions(newPriceID: string) {
		setCurrentPriceID(newPriceID);
		subscriptionHistory.refetch().then();
		setPlanPurchaseUnderway(false);
		setProcessingModalActive(false);
		setSelectedPlanName("");
		setSelectedPriceID("");
		setPurchaseConfirmModalActive(false);
		successAlertRef.current?.show(
			`Your subscription plan has been switched successfully!`
		);
	}

	function paymentFailedActions(errorMsg: string | undefined) {
		setPlanPurchaseUnderway(false);
		setSelectedPlanName("");
		setSelectedPriceID("");
		setPurchaseConfirmModalActive(false);
		if (errorMsg) {
			errorAlertRef.current?.show(
				`Error while trying to purchase new plan: ${errorMsg}`
			);
		} else {
			errorAlertRef.current?.show(
				"Oops! Something went wrong while processing your payment. Please contact support for further assistance"
			);
		}
	}

	function purchasePlanHandler(priceID: string) {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();
		setPlanPurchaseUnderway(true);

		// Upgrade/Downgrade if user has a payment method existing in stripe. Otherwise send them for checkout.
		if (pageData.has_payment_method) {
			// --------------------------- UPGRADE/DOWNGRADE USER PLAN ---------------------------
			changePlan.mutate(priceID, {
				onSuccess: (response) => {
					let success: boolean = response['data']['success']
					if (success) {
						let newPriceID: string = response['data']['new_price_id'];
						// Wait for some time to allow stripe webhook events to be done.
						setTimeout(() => {
							// refetch plan cards and table data in order to
							paymentSuccessActions(newPriceID);
						}, 7000);

					} else {
						let clientSecret: string = response['data']['client_secret']
						let paymentIntentStatus: string = response['data']['pi_status']
						let paymentIntentID: string = response['data']['pi_id']
						if ((paymentIntentStatus === 'requires_action') || (paymentIntentStatus === 'requires_payment_method')) {
							// This is for actions like 3D Secure auth.
							stripe?.handleNextAction({ clientSecret: clientSecret }).then((result) => {
								if (result.error) {
									// 3D Secure verification failed or card was declined
									if (result.error.code === "card_declined") {
										cardPaymentFailed.mutate(result.error.payment_intent?.id as string, {
											onSuccess: () => {
												paymentFailedActions(result.error.message);
											},
											onError: () => {
												paymentFailedActions(
													`Please contact us through support and let us know about this incident along with this id: ${paymentIntentID}`
												);
											}
										});
									} else {
										paymentFailedActions(
											`Please contact us through support and let us know about this incident along with this id: ${paymentIntentID}`
										);
									}
								} else {
									// 3D Secure verification successful
									paymentSuccessActions(priceID);
								}
							});
						} else if (paymentIntentStatus === 'card_declined') {
							// Card was declined
							cardPaymentFailed.mutate(paymentIntentID, {
								onSuccess: () => {
									paymentFailedActions(
										"Your card was declined. Please contact your card issuer for more information or try " +
										"purchasing again with a different card. You can update your card details by visiting Stripe " +
										"Customer Portal using the button on this page."
									);
								},
								onError: () => {
									paymentFailedActions(
										`Please contact us through support and let us know about this incident along with this id: ${paymentIntentID}`
									);
								}
							});
						} else {
							// Unhandled actions
							console.error(`Recieved unknown paymentIntentStatus '${paymentIntentStatus}'`);
							paymentFailedActions(undefined);
						}
					}
				},
				onError: (error) => {
					console.error(`Error while trying to purchase new plan: ${error}`);
					resetPaymentActions();
					errorAlertRef.current?.show(`Error while trying to purchase new plan: ${error}`);
				}
			});
		} else {
			// --------------------------- START CHECKOUT SESSION ---------------------------
			let successURL = process.env.REACT_APP_STRIPE_CHECKOUT_SUCCESS_URL;
			let cancelURL = process.env.REACT_APP_STRIPE_CHECKOUT_CANCEL_URL;

			if (successURL && cancelURL && priceID) {
				purchasePlan.mutate({
					priceID: priceID,
					successURL: successURL,
					cancelURL: cancelURL
				}, {
					onSuccess: (response) => {
						window.location.href = response['data']['checkout_url'];
					},
					onError: (error) => {
						console.error(`Stripe Checkout Failed: ${error}`);
					}
				});
			} else {
				errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for support.");
			}
		}
	}

	function getUsageStatIcon(statName: string) {
		if (statName === "Websites Connected") {
			return <Icon iconName={"web-globe"} />

		} else if (statName === "All Time Articles Generated") {
			return <Icon iconName={"draw-text-field"} />

		} else if (statName === "Articles (Usage this Month)") {
			return <Icon iconName={"article-icon"} width="2.1em" />

		} else if (statName === "Keywords (Usage this Month)") {
			return <Icon iconName={"keyword-research-icon"} width="2.1rem" />
		}
	}

	// Ordinal suffix to a number (e.g., 1 → 1st, 2 → 2nd, 3 → 3rd)
	const getOrdinal = (n: number): string => {
		const suffix = (n % 10 === 1 && n % 100 !== 11) ? "st"
			: (n % 10 === 2 && n % 100 !== 12) ? "nd"
				: (n % 10 === 3 && n % 100 !== 13) ? "rd"
					: "th";
		return `${n}${suffix}`;
	};

	// Format date string like "12 Jun 2025" into "12th Jun"
	const formatDayMonth = (dateStr: string) => {
		const [dayStr, month] = dateStr.split(" ");
		const day = parseInt(dayStr, 10);
		return `${getOrdinal(day)} ${month}`;
	};


	// ||---Subscription --- Plan --- Button---||
	const getButtonPlan = (plan: PlanData) => {
		if (currentPriceAmount == null) return "Price Unavailable";

		if (pageData.current_active_price_id_on_db != currentPriceID) {
			if (pageData.current_active_price_id_on_db === plan.price_id) {
				return "Current Plan";
			} else if (currentPriceID === plan.price_id) {
				return "Scheduled";
			}
		}

		if (currentPriceAmount === plan.price_amount) {
			return (pageData.ltd_enabled && currentPriceAmount === 0) ? "Disabled" : "Current Plan";
		}

		if (currentPriceAmount === 0) return "Subscribe Now";
		if (plan.price_amount === 0) return "Switch to Free Plan";
		return currentPriceAmount > plan.price_amount ? "Downgrade" : "Upgrade";
	};

	function formatLtdPlans(plans: Array<string>): string {
		if (!plans || plans.length === 0) return '';

		if (plans.length === 1) return plans[0];

		if (plans.length === 2) return `${plans[0]} & ${plans[1]}`;

		return `${plans.slice(0, -1).join(', ')} & ${plans[plans.length - 1]}`;
	}

	// Function to format INR prices in k format
	function formatINRPrice(priceInPaise: number): string {
		const priceInRupees = priceInPaise / 100;
		if (priceInRupees >= 1000) {
			const priceInK = priceInRupees / 1000;
			// Format to remove unnecessary decimal places
			if (priceInK % 1 === 0) {
				return `${priceInK}k`;
			} else {
				return `${priceInK.toFixed(1).replace(/\.?0+$/, '')}k`;
			}
		}
		return priceInRupees.toString();
	}

	// Clean pricing display function
	function formatPlanPrice(plan: PlanData, isDiscounted: boolean = false): string {
		let displayAmount = plan.price_amount;

		// For annual plans, divide by 12 to show per-month cost
		if (plan.annual_plan) {
			displayAmount = Math.round(displayAmount / 12);
		}

		// Apply discount if specified (50% off)
		if (isDiscounted) {
			displayAmount = Math.round(displayAmount * 0.5);
		}

		// Format based on currency
		if (plan.currency_code === 'inr') {
			return `₹${formatINRPrice(displayAmount)}`;
		} else {
			const dollarAmount = Math.round(displayAmount / 100);
			return `$${dollarAmount}`;
		}
	}

	return (
		<>
			<Helmet>
				<title>Subscription | Abun.com</title>
				<meta
					name="description"
					content="View and manage your current Abun subscription and billing."
				/>
			</Helmet>

			{/* -------------------------- PLAN PURCHASE CONFIRMATION -------------------------- */}
			<AbunModal active={purchaseConfirmModalActive}
				headerText={""}
				closeable={false}
				hideModal={() => {
					setPurchaseConfirmModalActive(false)
				}}>
				{planPurchaseUnderway ?
					<>
						<div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
							<AbunLoader show={purchaseConfirmModalActive} height="20vh" />
						</div>
						<p className={"has-text-centered is-size-4"}>
							{/* Moving to <b>{selectedPlanName.toUpperCase()}</b> Plan. Please wait... */}
							Moving to <b>{selectedPlanName === "Trial" ? "Free" : selectedPlanName.toUpperCase()}</b> Plan. Please wait...
						</p>
					</> :
					<>
						<h2 className={"is-size-4 font-secondary"}>
							Confirm Moving to <b>{selectedPlanName === "Trial" ? "Free" : selectedPlanName.toUpperCase()}</b> Plan?
						</h2>
						<p className={"mt-4"}>
							{
								selectedPriceAmount > currentPriceAmount ?
									<span>
										Once confirmed you will be charged full amount on your current default card and moved to <b>{selectedPlanName === "Trial" ? "Free" : selectedPlanName.toUpperCase()}</b> plan immeditately. All your current usage values will be reset back to 0 and your new billing cycle will start from today.
									</span> :
									<span>
										Your <b>{pageData.current_active_plan_name}</b> plan stays active until <b>{pageData.current_active_period_end_date}</b>. After that, you’ll be moved to the {selectedPlanName === "Trial" ? "Free" : selectedPlanName.toUpperCase()} plan. Are you sure you want to downgrade?
									</span>
							}
							<br />
							<br />
							<b>NOTE:</b> You can change your default card from Stripe Customer Portal before proceeding.
						</p>
						<div className={"mt-5"}>
							<GenericButton text={"Confirm"}
								type={"success"}
								clickHandler={() => {
									purchasePlanHandler(selectedPriceID);
								}}
							/>
							<GenericButton text={"Cancel"}
								type={"danger"}
								additionalClassList={["ml-5"]}
								clickHandler={() => {
									setPurchaseConfirmModalActive(false);
									setSelectedPlanName("");
									setSelectedPriceID("");
								}} />
						</div>
					</>}
			</AbunModal>

			{/* -------------------------- CANCEL SUBSCRIPTION CONFIRMATION -------------------------- */}
			<AbunModal
				active={cancelModalActive}
				headerText={""}
				closeable={false}
				hideModal={() => setCancelModalActive(false)}
			>
				<h2 className={"has-text-centered is-size-4 font-secondary"}>
					Cancel Subscription?
				</h2>
				<p className={"mt-4 has-text-centered"}>
					Your <b>{pageData.current_active_plan_name}</b> plan stays active until <b>{pageData.current_active_period_end_date}</b>. After that, you’ll be moved to the Free plan, and you won’t be charged again.
					Are you sure you want to cancel?
				</p>
				<div className={"mt-5"} style={{ display: "flex", width: "100%", justifyContent: "space-between" }}>
					<GenericButton
						text={"Yes, Cancel at End of Cycle"}
						type={"danger"}
						clickHandler={() => {
							purchasePlanHandler(selectedPriceID);
							setCancelModalActive(false);
							setProcessingModalActive(true);
						}}
					/>
					<GenericButton
						text={"Close"}
						type={"success"}
						additionalClassList={["ml-5", "subscription-cancel-btn"]}
						clickHandler={() => {
							setCancelModalActive(false);
						}}
					/>
				</div>
			</AbunModal>

			{/* -------------------------- CANCEL SUBSCRIPTION PROCESSING -------------------------- */}
			<AbunModal
				active={processingModalActive}
				headerText={""}
				closeable={false}
				hideModal={() => setProcessingModalActive(false)}
			>
				<div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
					<AbunLoader show={processingModalActive} height="20vh" />
				</div>
				<p className={"has-text-centered is-size-4"}>
					Processing cancellation request. Please wait...
				</p>
			</AbunModal>

			{/* -------------------------- PAYMENT FAILURE ALERT -------------------------- */}
			{(pageData.latest_invoice_is_open && stripePortalLink) &&
				<div className={"card w-100 has-background-danger mb-5"}>
					<div className={"card-content"}>
						<div className={"content"}>
							<p className={"has-text-centered has-text-white"}>
								Your previous plan renewal <b>failed</b> due to card issue.<br />
								To stay on current plan please update your payment details
								using <a className={"has-text-white is-underlined has-text-weight-bold"}
									target={"_blank"}
									rel={"noreferrer"}
									href={stripePortalLink}>Stripe Customer Portal</a>. Failing to do so will result in
								cancellation of your current plan.
							</p>
						</div>
					</div>
				</div>}

			<div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
				<ul>
					<li className={activeTab === "plans" ? "is-active" : ""}>
						<a onClick={() => setActiveTab("plans")}>Plans</a>
					</li>
					<li className={activeTab === "billing" ? "is-active" : ""}>
						<a onClick={() => setActiveTab("billing")}>Billing History</a>
					</li>
					<li className={activeTab === "usage-credit" ? "is-active" : ""}>
						<a onClick={() => setActiveTab("usage-credit")}>Usage & Credits</a>
					</li>
				</ul>
			</div>

			{/* -------------------------- Usage Stats -------------------------- */}
			{activeTab === "usage-credit" && (
				<section id={"usage-stats"} className={"usage-stats-section mt-2"}>
					<label htmlFor={"usage-stats"} className="section-label">
						Usage Stats For This Month: {pageData.ltd_enabled && `Appsumo (${formatLtdPlans(pageData.ltd_plans)})`}
					</label>
					<div className={"w-100 mt-5"}>
						<div className={"content"}>
							<div className={"usage-stats-container"}>
								{pageData.usage_stats.map(stat => {
									return (
										<>
											{stat.title === "Websites Connected" || stat.title === "All Time Articles Generated" ?
												<div className={"usage-stats-item is-flex is-flex-direction-column is-justify-content-space-between "} key={stat.title} style={{
													order: stat.title === "Websites Connected" ? 1 :
														stat.title === "All Time Articles Generated" ? 2 :
															stat.title === "Articles" ? 3 : 4
												}}>

													<p className={"usage-stats-item--title"}>{stat.title}</p>
													<div className={"usage-stats-item--info"}>
														<p className={"is-flex is-align-items-center is-justify-content-center usage-stats-item--value"}>
															{getUsageStatIcon(stat.title)}&nbsp;&nbsp;&nbsp;
															<span className="stat-value" style={{ fontSize: '2.5rem' }}>{stat.value}</span>
														</p>
													</div>
												</div>
												:
												<div className="usage-stats-item is-flex is-flex-direction-column is-justify-content-space-between" key={stat.title} style={{ order: 4, display: 'block' }}>
													<p className={"usage-stats-item--title"}>
														{stat.title === "Articles (Usage this Month)"
															? "Article Credits (Usage this Month)"
															: stat.title === "Keywords (Usage this Month)"
																? "Keyword Credits (Usage this Month)"
																: stat.title}
													</p>
													<div className={"usage-stats-item--info"}>
														<p className="my-1 is-flex">
															{getUsageStatIcon(stat.title)}&nbsp;&nbsp;&nbsp;<span className="is-size-2 is-flex">{stat.value}/{!stat.unlimited ? <p className="is-size-2">{stat.max}</p> : <p className="is-size-3">∞*</p>} </span>
														</p>
														{!stat.unlimited && tableData?.[0]?.current_period_end ? <p className="mt-2 is-align-self-center">(Renews on {formatDayMonth(tableData[0].current_period_end)})</p> : ""}
													</div>
												</div>
											}
										</>
									)
								})}
							</div>
						</div>
					</div>
				</section>
			)}

			{/* -------------------------- ACTIVE SUBSCRIPTION TABLE -------------------------- */}
			{activeTab === "billing" && (
				<div className={"w-100 mt-5 billing-container"}>
					<div className={"is-flex is-align-items-center is-justify-content-space-between w-100 mt-5 billing-header"}>
						<h3 >Billing History</h3>
						{stripePortalLink && <div className="btns-stripe-portal">
							<Link to={stripePortalLink}
								target={"_blank"}
								className={"has-text-black"}>
								View & manage billing →
							</Link>
						</div>}
					</div>

					<div className={"subscription-page-abun-table mt-2"}>
						<div className={"custom-abun-table"}>
							<AbunTable tableContentName={"Subscriptions"}
								tableData={filteredTableData}
								columnDefs={columnDefs}
								hidePagination={true}
								// pageSizes={pageSizes}
								// initialPageSize={pageSizes[6]}
								pageSizes={[]}
								initialPageSize={pageSizes[6]}
								noDataText={"No subscription data available."}
							// searchboxPlaceholderText={"Search subscriptions..."} 
							/>
						</div>
					</div>
				</div>
			)}

			{/* -------------------------- PLAN CARDS -------------------------- */}
			{activeTab === "plans" && (
				<div className={"w-100 mt-6"}>
					<div className="has-text-centered subscription-page-header">
						<h3 className="mb-5 has-text-weight-bold is-size-1">Access All Tools for One Price.</h3>

						<div className="tabs is-centered">
							<ul>
								<li className={activePricingTab === "monthly" ? "is-active" : ""}>
									<a onClick={() => setActivePricingTab("monthly")}>Monthly</a>
								</li>
								<li className={activePricingTab === "annual" ? "is-active" : ""}>
									<a onClick={() => setActivePricingTab("annual")}>Annual <span className="ml-1 has-text-weight-light"> (Limited Time Offer 6 Months Free)</span></a>
								</li>
							</ul>
						</div>

						<h2 className="my-5 has-text-weight-normal has-text-primary is-size-5">
							{activePricingTab === "monthly" ? "Limited Time Offer: 50% OFF for First Month. No Coupon Code Required."
								: "Pay for 6 Months, Get 6 Months for Free. No Coupon Code Required."
							}
						</h2>

					</div>
					{/* <div className={"offer-card"} >
					<h2 className="offer">Limited Time Offer: <span style={{ color: 'black' }}>50% OFF for First Month. </span> No Coupon Required.</h2>
				</div> */}

					{allPlanData.isLoading ? <p className={"has-text-centered"} style={{ fontSize: "1.5em" }}>
						<AbunLoader show={allPlanData.isLoading} height="20vh" />
					</p> :
						<>
							<div className={"subscription-manager-plan-cards-container-new-pricing"}>
								{planCardsData
									.filter(plan => {
										// Filter plans based on active pricing tab and annual_plan field
										if (activePricingTab === "annual") {
											return plan.annual_plan === true;
										} else {
											return plan.annual_plan === false;
										}
									})
									.map(plan => {
										return (
											<div
												className={plan.name === "Growth Max" ? "plan-card popular" : "plan-card"}
												key={plan.id}
												id={plan.name.toLowerCase()}>
												<div className="is-flex is-flex-direction-column" style={{ gap: '10px' }}>

													{/* ---------- PLAN NAME ----------- */}
													<h5 className={"plan-name"}>
														{plan.name}
													</h5>

													{/* ----------- PLAN CUTTING PRICE (Original Price) ------------- */}
													<div className={"is-flex is-align-items-center pricing-gap"}>
														<p className={"cancel-pricing"}>
															{formatPlanPrice(plan, false)}
														</p>
													</div>

												</div>

												{/* ------------  Plan Pricing (Discounted) ------------  */}
												<div className={"is-flex is-align-items-center pricing-gap"}>
													<p className={"pricing"}>
														{formatPlanPrice(plan, true)}
														<span className={"pricing-suffix"}> {activePricingTab === "monthly" ? "first month" : "per month"}</span>
													</p>
												</div>

												{/* ------- Show when User is on ANNUAL TAB ---------- */}
												{activePricingTab === "annual" &&
													<span style={{ marginTop: '-22px', marginBottom: '14px' }}>Billed Yearly</span>
												}

												<div className={"plan-details"}>
													<PricingCard plan={plan} />
												</div>

												{/* ---------- Plans Button ----------- */}
												<div>
													<button className="mb-2 button is-primary is-outlined plan-details--buy-button">
														PROCEED
													</button>
												</div>
											</div>
										)
									})}
							</div>
						</>}
				</div>
			)}


			<ErrorAlert ref={errorAlertRef} />
			<SuccessAlert ref={successAlertRef} />
		</>
	)
}


const Tag = ({ children }: { children: React.ReactNode }) => (
	<span className="tag is-rounded has-text-black ml-2" style={{ background: '#FAC44B', fontSize: '14px', lineHeight: '1.2' }}>{children}</span>
);

const ComingSoon = () => (
	<span
		className="tag is-light is-rounded ml-2"
		style={{ fontSize: "14px", color: '#686868', background: '#F1F1F1' }}
	>
		Coming Soon
	</span>
);

const PricingCard = ({ plan }: { plan: PlanData }) => {
	return (
		<div style={{ color: '#000c60' }}>
			<div className="mt-4">
				<p className="mb-3 plan-article-gradient"><b>{plan.articles}</b> AI Articles</p>
				<p className="mb-3 plan-article-gradient"><b>{plan.credits}</b> Keyword Research Credits</p>
				<p className="mb-3">
					{typeof plan.sites === "number" ? (
						<span className="plan-article-gradient"><b>{plan.sites}</b> Sites</span>
					) : (
						<span className="is-flex is-align-items-center is-justify-content-space-between">
							<b className="plan-article-gradient">Sites</b> <Tag>Unlimited</Tag>
						</span>
					)}
				</p>
				<p className="mb-3 plan-article-gradient"><b>Access to All Tools</b></p>
				<p className="mb-3 plan-article-gradient"><b>All Integrations</b></p>
			</div>

			<hr />

			<ul>
				{plan.features.map((feature: PlanFeature) => (
					<li key={feature.label} className={` ${plan.name === "Growth Max" ? "mb-2" : "mb-3 mr-3"}`}>
						{feature.value === "Unlimited" ? (
							<span className="is-flex is-align-items-center is-justify-content-space-between">
								{feature.label}
								<Tag>Unlimited</Tag>
							</span>
						) : (
							<>
								<b>{feature.value}</b> {feature.label}
							</>
						)}
					</li>
				))}
			</ul>

			<ul>
				{plan.tools.map((tool: PlanTool) => (
					<li key={tool.label} className={`mb-3 ${plan.name !== "Growth Max" ? "mr-3" : ""}`}>
						{!tool.comingSoon ? (
							<span className="is-flex is-align-items-center">
								<img src={checkCircle} alt="Check Mark" style={{ width: '24px', marginRight: '0.95rem' }} />
								{tool.value && <b className="mr-1">{tool.value}</b>}
								{tool.label}
							</span>
						) : (
							<span className="is-flex is-align-items-center is-justify-content-space-between">
								<span>
									<img src={checkCircle} alt="Check Mark" style={{ width: '24px', marginRight: '0.95rem' }} />
									{tool.label}
								</span>
								{tool.comingSoon && <ComingSoon />}
							</span>
						)}
					</li>
				))}
			</ul>
		</div>
	);
};